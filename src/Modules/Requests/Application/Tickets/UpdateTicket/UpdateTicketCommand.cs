using MediatR;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Tickets.UpdateTicket;

public record UpdateTicketCommand : IRequest<Result>
{
    public Guid Id { get; init; }
    public TicketType TicketType { get; init; }
    public Guid? TopTicketId { get; init; }
    public Guid SubjectId { get; init; }
    public required string Title { get; init; }
    public string? Description { get; init; }
    public Guid? NotificationWayId { get; init; }
    public Guid? UserId { get; init; }
    public List<Guid> DepartmentIds { get; init; } = [];
    public List<UpdateTicketFileDto> TicketFiles { get; init; } = [];
    public PriorityEnum Priority { get; init; }
    public DateTime? EndDate { get; init; }
    public List<Guid>? Watchlist { get; init; }
    public List<string> Tags { get; init; } = [];
    public Dictionary<string, string>? AttributeData { get; init; }
}

public record UpdateTicketFileDto
{
    public Guid? Id { get; init; } // Null ise yeni dosya, de<PERSON><PERSON>e güncelleme
    public Guid FileId { get; init; }
    public required string FileName { get; init; }
    public required string FilePath { get; init; }
}
