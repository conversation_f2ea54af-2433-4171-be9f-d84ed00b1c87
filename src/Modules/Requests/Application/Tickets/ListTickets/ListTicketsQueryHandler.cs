using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Shared.Application;
using Shared.Contracts;

namespace Requests.Application.Tickets.ListTickets;

public class ListTicketsQueryHandler(
    IRequestsDbContext dbContext,
    ISharedUserService userService,
    ISharedCustomerService customerService,
    ISharedDepartmentService departmentService
) : IRequestHandler<ListTicketsQuery, PagedResult<TicketListItemDto>>
{
    private readonly IRequestsDbContext _dbContext = dbContext;
    private readonly ISharedUserService _userService = userService;
    private readonly ISharedCustomerService _customerService = customerService;

    public async Task<PagedResult<TicketListItemDto>> Handle(ListTicketsQuery request, CancellationToken cancellationToken)
    {
        var query = _dbContext.Tickets.AsQueryable();

        // Filtreleri uygula
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.Trim().ToLower();
            query = query
                .Where(t => t.Title.ToLower().Contains(searchTerm) ||
                            t.Description.ToLower().Contains(searchTerm));
        }
        if (!string.IsNullOrWhiteSpace(request.Title))
        {
            var title = request.Title.Trim().ToLower();
            query = query.Where(t => t.Title.ToLower().Contains(title));
        }
        if (request.SubjectId.HasValue)
        {
            query = query.Where(t => t.SubjectId == request.SubjectId.Value);
        }
        if (request.CustomerId.HasValue)
        {
            query = query.Where(t => t.CustomerId == request.CustomerId.Value);
        }
        if (request.UserId.HasValue)
        {
            query = query.Where(t => t.UserId == request.UserId.Value);
        }
        if (request.Priority.HasValue)
        {
            query = query.Where(t => t.Priority == request.Priority.Value);
        }
        if (request.StartDate.HasValue)
        {
            query = query.Where(t => t.InsertDate >= request.StartDate.Value);
        }
        if (request.EndDate.HasValue)
        {
            query = query.Where(t => t.InsertDate <= request.EndDate.Value);
        }
        if (request.DepartmentIds?.Length > 0)
        {
            query = query.Where(t => t.TicketDepartment.Any(td => request.DepartmentIds.Contains(td.DepartmentId)));
        }
        if (request.TopTicketId.HasValue)
        {
            query = query.Where(t => t.TopTicketId >= request.TopTicketId);
        }

        var totalCount = await _dbContext.Tickets.CountAsync(cancellationToken);
        var filteredCount = await query.CountAsync(cancellationToken);
        var tickets = await query
            .OrderByDescending(t => t.InsertDate)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);
        var subjectIds = tickets.Select(t => t.SubjectId).Distinct().ToList();
        var subjects = await _dbContext.TicketSubjects
            .Where(s => subjectIds.Contains(s.Id))
            .ToDictionaryAsync(s => s.Id, s => s.Name, cancellationToken);
        var ticketIds = tickets.Select(t => t.Id).ToList();
        var commentCounts = await _dbContext.TicketComments
            .Where(c => ticketIds.Contains(c.TicketId))
            .GroupBy(c => c.TicketId)
            .Select(g => new { TicketId = g.Key, Count = g.Count() })
            .ToDictionaryAsync(g => g.TicketId, g => g.Count, cancellationToken);
        var userIds = tickets.Where(t => t.UserId.HasValue)
            .Select(t => t.UserId!.Value)
            .Concat(tickets.Select(t => t.ReporterUserId))
            .Distinct()
            .ToList();
        var customerIds = tickets.Select(t => t.CustomerId).Distinct().ToList();
        var users = new Dictionary<Guid, SharedUserDTO>();
        var customers = new Dictionary<Guid, SharedCustomerDTO>();
        var userResult = await _userService.GetUsersByIdsAsync(userIds);
        users = userResult.ToDictionary(u => u.Id, u => u);
        var customerResult = await _customerService.GetCustomerByIdsAsync(customerIds);
        customers = customerResult.Value.ToDictionary(c => c.Id, c => c);
        var notificationWay = await _customerService.GetNotificationWaysAsync();
        var result = tickets.Select(t => new TicketListItemDto
        {
            Id = t.Id,
            SubjectId = t.SubjectId,
            SubjectName = subjects.TryGetValue(t.SubjectId, out var subjectName) ? subjectName : null,
            CustomerId = t.CustomerId,
            CustomerName = customers.TryGetValue(t.CustomerId, out var customer) ? $"{customer.Name} {customer.Surname}" : null,
            Title = t.Title,
            Description = t.Description,
            NotificationWayId = t.NotificationWayId,
            NotificationWay = notificationWay.Value.FirstOrDefault(x => x.Id == t.NotificationWayId)?.Name,
            UserId = t.UserId,
            UserName = t.UserId.HasValue && users.TryGetValue(t.UserId.Value, out var user) ? $"{user.Name} {user.Surname}" : null,
            ReporterUserId = t.ReporterUserId,
            ReporterUserName = users.TryGetValue(t.ReporterUserId, out var reporter) ? $"{reporter.Name} {reporter.Surname}" : null,
            Priority = t.Priority,
            StatusId = t.StatusId,
            StatusName = t.Status?.Name,
            EndDate = t.EndDate,
            InsertDate = t.InsertDate,
            CommentCount = commentCounts.TryGetValue(t.Id, out var count) ? count : 0,
            AttributeData = t.AttributeData,
            Departments = [.. t.TicketDepartment.Select(td => new DepartmentDTO
            {
                DepartmentId = td.DepartmentId,
                Name = td.DepartmentName
            })]
        }).ToList();
        var pagedResult = PagedResult<TicketListItemDto>.Success(result);
        pagedResult.PageNumber = request.PageNumber;
        pagedResult.PageSize = request.PageSize;
        pagedResult.Count = totalCount;
        pagedResult.FilteredCount = filteredCount;
        return pagedResult;
    }
}
