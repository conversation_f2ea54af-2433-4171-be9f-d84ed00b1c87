using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;

namespace Requests.Application.Tickets.CreateTicket;

public class CreateTicketCommandValidator : AbstractValidator<CreateTicketCommand>
{
    private readonly IRequestsDbContext _dbContext;

    public CreateTicketCommandValidator(IRequestsDbContext dbContext)
    {
        _dbContext = dbContext;

        RuleFor(x => x.SubjectId)
            .NotEmpty().WithMessage("Konu seçilmelidir.")
            .MustAsync(BeExistingSubject).WithMessage("Seçilen konu bulunamadı.");

        RuleFor(x => x.CustomerId)
            .NotEmpty().WithMessage("Müşteri seçilmelidir.");

        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("Başlık boş olamaz.")
            .MaximumLength(200).WithMessage("Başlık 200 karakterden uzun olamaz.");

        // RuleFor(x => x.Description)
        //     .NotEmpty().WithMessage("Açıklama boş olamaz.");

        RuleFor(x => x.Priority)
            .IsInEnum().WithMessage("Geçersiz öncelik değeri.");

        RuleFor(x => x.AttributeData)
            .Must(BeValidAttributeData).WithMessage("AttributeData alanında geçersiz key-value çifti bulunuyor.")
            .When(x => x.AttributeData != null);
    }

    private async Task<bool> BeExistingSubject(Guid subjectId, CancellationToken cancellationToken)
    {
        return await _dbContext.TicketSubjects
            .AnyAsync(s => s.Id == subjectId, cancellationToken);
    }

    private static bool BeValidAttributeData(Dictionary<string, string>? attributeData)
    {
        if (attributeData == null)
            return true;

        foreach (var kvp in attributeData)
        {
            // Key boş olamaz ve maksimum 100 karakter olabilir
            if (string.IsNullOrWhiteSpace(kvp.Key) || kvp.Key.Length > 100)
                return false;

            // Value null olabilir ama boş değilse maksimum 500 karakter olabilir
            if (kvp.Value != null && kvp.Value.Length > 500)
                return false;
        }

        return true;
    }
}
