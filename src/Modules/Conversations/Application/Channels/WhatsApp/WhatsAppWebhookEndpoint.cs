using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Shared.Endpoints;

namespace Conversations.Application.Channels.WhatsApp;

internal sealed class WhatsAppWebhookEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/conversations/webhooks/whatsapp", async (

        )
            // [FromBody] WhatsAppDTO whatsapp,
            // WhatsAppWebhookHandler handler)
            =>
        {
            using var reader = new StreamReader(request.Body);
            string jsonString = await reader.ReadToEndAsync();
            // var request = new WhatsAppWebhookRequest()
            // {
            //     Type = whatsapp.Entry[0].Changes[0].Field,
            //     MessageId = whatsapp.Entry[0].Changes[0].Value.Messages[0].Id,
            //     //ConversationId = whatsapp.Entry[0].Changes[0].Value.Messages[0].ConversationId,
            //     CustomerPhone = whatsapp.Entry[0].Changes[0].Value.Metadata.DisplayPhoneNumber,
            //     From = whatsapp.Entry[0].Changes[0].Value.Messages[0].From,
            //     To = whatsapp.Entry[0].Changes[0].Value.Messages[0].To,
            //     Content = whatsapp.Entry[0].Changes[0].Value.Messages[0].Text?.Body,
            //     ContentType = whatsapp.Entry[0].Changes[0].Value.Messages[0].Type,
            //     Timestamp = DateTimeOffset.FromUnixTimeMilliseconds(long.Parse(whatsapp.Entry[0].Changes[0].Value.Messages[0].Timestamp)).UtcDateTime,
            //     MetaData = new Dictionary<string, object>
            //     {
            //         { "display_phone_number", whatsapp.Entry[0].Changes[0].Value.Metadata.DisplayPhoneNumber },
            //         { "phone_number_id", whatsapp.Entry[0].Changes[0].Value.Metadata.PhoneNumberId }
            //     }
            // };
            // return await handler.HandleWebhookAsync(request);
            return Results.Ok();
        })
        .WithTags("Conversations.Chats")
        .WithGroupName("apiv1")
        .Produces(StatusCodes.Status202Accepted)
        .AllowAnonymous();
    }

}
